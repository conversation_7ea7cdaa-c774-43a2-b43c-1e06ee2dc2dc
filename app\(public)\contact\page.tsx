"use client";

import React, { useState } from 'react';
import Image from 'next/image';

const contactInfo = [
    {
        icon: '📧',
        title: 'Email Us',
        description: 'Send us an email and we will get back to you within 24 hours.',
        contact: '<EMAIL>',
        action: 'mailto:<EMAIL>',
        bgColor: 'from-blue-50 to-blue-100',
        iconBg: 'bg-blue-100',
        hoverBg: 'hover:from-blue-100 hover:to-blue-200'
    },
    {
        icon: '📞',
        title: 'Call Us',
        description: 'Speak directly with our team during business hours.',
        contact: '+91 6204351245',
        action: 'tel:+916204351245',
        bgColor: 'from-green-50 to-green-100',
        iconBg: 'bg-green-100',
        hoverBg: 'hover:from-green-100 hover:to-green-200'
    },
    {
        icon: '📍',
        title: 'Visit Us',
        description: 'Come visit our office for an in-person conversation.',
        contact: '01, Ground Floor, BOI Audit Office Building, <PERSON>raga<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ranchi - 834001',
        action: '#',
        bgColor: 'from-purple-50 to-purple-100',
        iconBg: 'bg-purple-100',
        hoverBg: 'hover:from-purple-100 hover:to-purple-200'
    },
    {
        icon: '💬',
        title: 'Live Chat',
        description: 'Chat with our team in real-time for quick questions.',
        contact: 'Available 9 AM - 6 PM IST',
        action: '#',
        bgColor: 'from-orange-50 to-orange-100',
        iconBg: 'bg-orange-100',
        hoverBg: 'hover:from-orange-100 hover:to-orange-200'
    }
];

const ContactPage = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        setTimeout(() => {
            setIsSubmitting(false);
            setSubmitStatus('success');
            setFormData({
                name: '',
                email: '',
                phone: '',
                subject: '',
                message: ''
            });

            setTimeout(() => setSubmitStatus('idle'), 3000);
        }, 2000);
    };

    return (
        <main className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50">
            {/* Hero Section */}
            <section className="relative py-24 lg:py-32 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/30"></div>
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
                    <div className="grid items-center gap-16 lg:grid-cols-2">
                        <div className="space-y-8">
                            <div className="space-y-6">
                                <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 text-sm font-medium text-gray-700">
                                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                                    Professional Support Team
                                </div>
                                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                                    Get in{' '}
                                    <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                        Touch
                                    </span>
                                </h1>
                                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                                    We would love to hear from you! Whether you have questions about current opportunities,
                                    need more information about our company, or simply want to share your thoughts,
                                    our team is here to help.
                                </p>
                                <div className="flex items-center gap-6 pt-4">
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                        <span>Available 24/7</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                        <span>Quick Response</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="relative">
                            <div className="absolute -inset-6 rounded-3xl bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-20 blur-2xl"></div>
                            <div className="relative rounded-3xl bg-white p-2 shadow-2xl">
                                <Image
                                    src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=600&h=600&fit=crop"
                                    width={500}
                                    height={500}
                                    className="rounded-2xl"
                                    alt="Contact us"
                                    priority
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact Methods Section */}
            <section className="py-24 lg:py-32 bg-white relative">
                <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent"></div>
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
                    <div className="text-center mb-20">
                        <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 text-sm font-medium text-gray-700 mb-6">
                            <span className="text-lg">🌟</span>
                            Multiple Contact Options
                        </div>
                        <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl mb-4">
                            Choose Your Preferred{' '}
                            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                Communication
                            </span>
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            We offer multiple ways to get in touch. Select the method that works best for you and we'll respond promptly.
                        </p>
                    </div>
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                        {contactInfo.map((info, index) => (
                            <div key={index} className={`group relative rounded-2xl bg-gradient-to-br ${info.bgColor} p-8 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${info.hoverBg} border border-white/50 backdrop-blur-sm`}>
                                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                <div className="relative">
                                    <div className={`mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl ${info.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}>
                                        <span className="text-3xl">{info.icon}</span>
                                    </div>
                                    <h3 className="mb-3 text-xl font-bold text-gray-900 group-hover:text-gray-800 transition-colors">{info.title}</h3>
                                    <p className="mb-6 text-sm text-gray-700 leading-relaxed">{info.description}</p>
                                    <a
                                        href={info.action}
                                        className="inline-block text-sm font-semibold text-gray-800 hover:text-gray-900 transition-all duration-300 break-words hyphens-auto max-w-full bg-white/50 rounded-lg px-3 py-2 hover:bg-white/70"
                                        style={{ wordBreak: 'break-word' }}
                                    >
                                        {info.contact}
                                    </a>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Contact Form Section */}
            <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 relative overflow-hidden">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] pointer-events-none"></div>
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] pointer-events-none"></div>

                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
                    {/* Section Header */}
                    <div className="text-center mb-16">
                        <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 text-sm font-medium text-gray-700 mb-6">
                            <span className="text-lg">✉️</span>
                            Direct Communication
                        </div>
                        <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl mb-4">
                            Get in{' '}
                            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                Touch
                            </span>
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Ready to start a conversation? Fill out the form below and we'll get back to you as soon as possible.
                        </p>
                    </div>

                    <div className="grid items-start gap-16 lg:grid-cols-2">
                        {/* Left Side - Image */}
                        <div className="relative order-2 lg:order-1">
                            <div className="absolute -inset-6 rounded-3xl bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-20 blur-2xl"></div>
                            <div className="relative rounded-3xl bg-white p-3 shadow-2xl">
                                <Image
                                    src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=600&fit=crop"
                                    width={500}
                                    height={500}
                                    className="rounded-2xl"
                                    alt="Contact form"
                                />
                            </div>

                            {/* Floating Elements */}
                            <div className="absolute -top-4 -right-4 bg-white rounded-2xl p-4 shadow-xl">
                                <div className="flex items-center gap-2">
                                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                                    <span className="text-sm font-medium text-gray-700">Online Now</span>
                                </div>
                            </div>

                            <div className="absolute -bottom-4 -left-4 bg-white rounded-2xl p-4 shadow-xl">
                                <div className="flex items-center gap-2">
                                    <span className="text-lg">⚡</span>
                                    <span className="text-sm font-medium text-gray-700">Quick Response</span>
                                </div>
                            </div>
                        </div>

                        {/* Right Side - Form Card */}
                        <div className="order-1 lg:order-2">
                            <div className="relative">
                                {/* Card Background with Gradient Border */}
                                <div className="absolute -inset-1 rounded-3xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-20 blur-lg"></div>
                                <div className="relative rounded-3xl bg-white/80 backdrop-blur-xl border border-white/50 shadow-2xl p-8 lg:p-10">
                                    {/* Form Header */}
                                    <div className="text-center mb-6">
                                        <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 mb-3">
                                            <span className="text-lg text-gray-600">📝</span>
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Reach Out Confidentially</h3>
                                        <p className="text-gray-600">Your privacy is our priority — your information is never shared, and we’ll never spam your inbox or phone.</p>

                                        {/* Privacy Badge */}
                                        <div className="inline-flex items-center gap-2 bg-green-50 border border-green-200 rounded-lg px-3 py-2 mb-3 mt-3">
                                            <div className="flex-shrink-0">
                                                <span className="text-green-600 text-sm">🔒</span>
                                            </div>
                                            <span className="text-xs font-medium text-green-800">Your data is safe with us</span>
                                        </div>

                                      
                                    </div>

                                    {/* Success Message */}
                                    {submitStatus === 'success' && (
                                        <div className="mb-4 rounded-lg bg-green-50 p-3 border border-green-200/50">
                                            <div className="flex items-center gap-2">
                                                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
                                                    <span className="text-green-600 text-xs">✓</span>
                                                </div>
                                                <p className="text-green-800 text-xs font-medium">
                                                    Message sent successfully!
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Contact Form - Single Column Compact */}
                                    <form onSubmit={handleSubmit} className="space-y-3">
                                        <div>
                                            <label htmlFor="name" className="block text-xs font-medium text-gray-700 mb-1">
                                                Full Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 focus:border-gray-400 focus:bg-white focus:outline-none focus:ring-1 focus:ring-gray-200/50 transition-all duration-300"
                                                placeholder="Your full name"
                                            />
                                        </div>

                                        <div>
                                            <label htmlFor="email" className="block text-xs font-medium text-gray-700 mb-1">
                                                Email Address *
                                            </label>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 focus:border-gray-400 focus:bg-white focus:outline-none focus:ring-1 focus:ring-gray-200/50 transition-all duration-300"
                                                placeholder="<EMAIL>"
                                            />
                                        </div>

                                        <div>
                                            <label htmlFor="phone" className="block text-xs font-medium text-gray-700 mb-1">
                                                Phone Number
                                            </label>
                                            <input
                                                type="tel"
                                                id="phone"
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleInputChange}
                                                className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 focus:border-gray-400 focus:bg-white focus:outline-none focus:ring-1 focus:ring-gray-200/50 transition-all duration-300"
                                                placeholder="Your phone number"
                                            />
                                        </div>

                                       

                                        <div>
                                            <label htmlFor="message" className="block text-xs font-medium text-gray-700 mb-1">
                                                Message *
                                            </label>
                                            <textarea
                                                id="message"
                                                name="message"
                                                value={formData.message}
                                                onChange={handleInputChange}
                                                required
                                                rows={3}
                                                className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 focus:border-gray-400 focus:bg-white focus:outline-none focus:ring-1 focus:ring-gray-200/50 transition-all duration-300 "
                                                placeholder="Your message..."
                                            />
                                        </div>

                                        <button
                                            type="submit"
                                            disabled={isSubmitting}
                                            className="w-full rounded-lg bg-gray-900 px-4 py-2.5 text-sm text-white font-medium shadow-sm transition-all duration-300 hover:bg-gray-800 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-gray-500/20"
                                        >
                                            {isSubmitting ? (
                                                <span className="flex items-center justify-center gap-2">
                                                    <svg className="animate-spin h-3 w-3" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                                    </svg>
                                                    Sending...
                                                </span>
                                            ) : (
                                                'Send Message'
                                            )}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Call to Action Section */}
            <section className="py-24 lg:py-32 bg-white relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30"></div>
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
                    <div className="text-center mb-16">
                        <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 text-sm font-medium text-gray-700 mb-6">
                            <span className="text-lg">🎯</span>
                            Next Steps
                        </div>
                        <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl mb-4">
                            What's{' '}
                            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                Next?
                            </span>
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Ready to take the next step? Here are some ways we can help you achieve your goals.
                        </p>
                    </div>

                    <div className="grid gap-8 md:grid-cols-2">
                        <div className="group relative rounded-3xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 border border-blue-100/50">
                            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-100/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-500 mb-6 group-hover:scale-110 transition-transform duration-500">
                                    <span className="text-2xl text-white">🚀</span>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-900 transition-colors">
                                    Ready to Start Your Journey?
                                </h3>
                                <div className="space-y-4 text-gray-700">
                                    <p className="leading-relaxed">
                                        Join our team of innovative professionals and be part of exciting projects that shape the future of technology.
                                    </p>
                                    <p className="leading-relaxed">
                                        Explore our current job openings and find the perfect role that matches your skills and ambitions.
                                    </p>
                                </div>
                                <div className="mt-6">
                                    <div className="inline-flex items-center gap-2 text-blue-600 font-semibold group-hover:gap-3 transition-all duration-300">
                                        <span>Explore Careers</span>
                                        <span className="text-lg">→</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="group relative rounded-3xl bg-gradient-to-br from-green-50 to-emerald-100 p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 border border-green-100/50">
                            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-green-100/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 mb-6 group-hover:scale-110 transition-transform duration-500">
                                    <span className="text-2xl text-white">💼</span>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-900 transition-colors">
                                    Have a Project in Mind?
                                </h3>
                                <div className="space-y-4 text-gray-700">
                                    <p className="leading-relaxed">
                                        Let us help you bring your ideas to life with our comprehensive IT solutions and expert development team.
                                    </p>
                                    <p className="leading-relaxed">
                                        From web applications to mobile apps, we have the expertise to deliver exceptional results.
                                    </p>
                                </div>
                                <div className="mt-6">
                                    <div className="inline-flex items-center gap-2 text-green-600 font-semibold group-hover:gap-3 transition-all duration-300">
                                        <span>View Our Services</span>
                                        <span className="text-lg">→</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default ContactPage;
